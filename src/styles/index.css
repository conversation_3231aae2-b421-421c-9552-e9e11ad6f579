:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #010313;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Z-Index 层级管理 */
  --z-base: 1;
  --z-map: 100;
  --z-map-controls: 1000;
  --z-map-popover: 1100;
  --z-dropdown: 1150;
  --z-modal: 1200;
  --z-toast: 1300;
  --z-tooltip: 1400;
  
  /* 下拉菜单相关变量 */
  --dropdown-shadow: 0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
  --dropdown-backdrop: blur(20px) saturate(180%);
  --dropdown-border-radius: 12px;
  --dropdown-padding: 8px 12px;
  --dropdown-animation-duration: 0.2s;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden; /* 只隐藏水平滚动条 */
  overflow-y: auto; /* 允许垂直滚动 */
  /* 防止过度滚动出现白色背景 */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: rgba(255, 255, 255, 0.87);
    background-color: #010313;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #1a1a1a;
  }
}

/* 全局下拉菜单基础样式 */
.dropdown-base {
  position: fixed !important;
  z-index: var(--z-dropdown) !important;
  overflow: visible !important;
  pointer-events: auto !important;
  will-change: transform, opacity;
  backdrop-filter: var(--dropdown-backdrop);
  border-radius: var(--dropdown-border-radius);
  box-shadow: var(--dropdown-shadow);
  animation: dropdownFadeIn var(--dropdown-animation-duration) ease;
}

/* 防止父容器截断下拉菜单的工具类 */
.overflow-visible {
  overflow: visible !important;
}

.dropdown-container {
  position: relative;
  overflow: visible !important;
}

/* 全局下拉菜单动画 */
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 确保所有下拉菜单不被截断 */
.nav-container,
.map-buttons-container,
.enhanced-map-controls,
.floating-navigation,
.user-menu-wrapper,
.language-selector {
  overflow: visible !important;
}

/* 修复body和html的overflow设置 */
html {
  overflow-x: hidden; /* 只隐藏水平滚动条 */
  overflow-y: auto; /* 允许垂直滚动 */
  /* 防止过度滚动出现白色背景 */
  overscroll-behavior: none;
  height: 100%;
}

/* 为所有现有的下拉菜单添加统一的z-index */
.dropdown-menu,
.user-dropdown,
.language-dropdown,
.language-menu,
.language-dropdown-menu,
.map-popover-content,
.map-filter-panel,
.map-search-panel {
  z-index: var(--z-dropdown) !important;
  overflow: visible !important;
  will-change: transform, opacity;
}

/* 确保地图弹出菜单有正确的层级 */
.map-popover-content {
  z-index: var(--z-map-popover) !important;
}

/* 响应式修复 */
@media (max-width: 768px) {
  .dropdown-base,
  .dropdown-menu,
  .user-dropdown,
  .language-dropdown,
  .language-menu,
  .language-dropdown-menu {
    max-width: calc(100vw - 20px);
    max-height: calc(100vh - 100px);
    overflow-y: auto;
    overflow-x: hidden;
  }
}

/* 引入增强下拉菜单样式 */
@import './EnhancedDropdown.css';

/* CPI 成功通知样式 */
.cpi-success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  padding: 16px 20px;
  background: linear-gradient(135deg, 
    rgba(40, 167, 69, 0.95) 0%, 
    rgba(34, 139, 60, 0.9) 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(40, 167, 69, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInFromRight 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-weight: 500;
  max-width: 280px;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.cpi-success-notification .notification-content {
  text-align: center;
  font-size: 14px;
  line-height: 1.4;
}

.cpi-success-notification small {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
}
